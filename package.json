{"name": "test-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@tanstack/react-form": "^1.19.0", "@tanstack/react-query": "^5.84.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "jsdom": "^26.1.0", "react": "^19.1.1", "react-dom": "^19.1.1", "styled-components": "^6.1.19", "ts-pattern": "^5.8.0", "vitest": "^3.2.4"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "5.9.2", "typescript-eslint": "8.39.0", "vite": "^7.1.0"}}