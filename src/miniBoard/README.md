# Mini Board Game

A simple React-based board game where players can move around a grid and try to visit all cells.

## Features

### Start Page

- **Column Number Input**: Set the number of columns for the board (1-20)
- **Row Number Input**: Set the number of rows for the board (1-20)
- **Start X Coordinate**: Set the starting X position (0-indexed)
- **Start Y Coordinate**: Set the starting Y position (0-indexed)
- **Input Validation**: Ensures coordinates are within board boundaries
- **Start Button**: Creates the board with the specified configuration

### Board Game

- **HTML Table Structure**: Proper table with `<table>`, `<tr>`, and `<td>` elements
- **Image-Based Interface**: Uses local PNG images from Icons8 instead of text symbols
- **Current Position**: 🐝 Bee image shows where the player currently is
- **Start Position**: ❤️ Heart image marks the initial starting position
- **Visited Cells**: ❌ Cross image (full opacity) shows visited positions
- **Empty Cells**: ❌ Cross image (faded) shows unvisited cells
- **Navigation Controls**: Left and right panel buttons for directional movement
- **Keyboard Support**: Arrow keys and WASD for navigation
- **Movement Rules**: Move one cell at a time in four directions (up, down, left, right)
- **Reset Functionality**: But<PERSON> to reset the game to initial state
- **Progress Tracking**: Shows current position and number of visited cells

## How to Play

1. **Setup**: On the start page, configure your board:

   - Choose board dimensions (columns and rows)
   - Set starting coordinates
   - Click "Start Game"

2. **Movement**:

   - Use navigation buttons on left/right panels
   - Use keyboard: Arrow keys or WASD
   - Move one cell at a time in four directions
   - Try to visit all cells on the board

3. **Visual Indicators**:

   - 🐝 Bee image: Your current position
   - ❤️ Heart image: Starting position
   - ❌ Cross image (full opacity): Cells you've visited
   - ❌ Cross image (faded): Empty cells
   - 🟡 Yellow background: Available moves

4. **Reset**: Use the "Reset Game" button to start over or "Back to Start" to change board configuration

## Game Objective

The goal is to visit all cells on the board by moving strategically. This is similar to a "Knight's Tour" problem but allows movement in all 8 directions to adjacent cells.

## Technical Details

- Built with React and TypeScript
- Responsive grid layout using CSS Grid
- State management for position tracking and visited cells
- Input validation and error handling
- Comprehensive test coverage

## Components

- `StartPage.tsx`: Configuration interface for setting up the game
- `Board.tsx`: Main game board component with movement logic
- Test files included for both components
