# Mini Board Game

A simple React-based board game where players can move around a grid and try to visit all cells.

## Features

### Start Page
- **Column Number Input**: Set the number of columns for the board (1-20)
- **Row Number Input**: Set the number of rows for the board (1-20)
- **Start X Coordinate**: Set the starting X position (0-indexed)
- **Start Y Coordinate**: Set the starting Y position (0-indexed)
- **Input Validation**: Ensures coordinates are within board boundaries
- **Start Button**: Creates the board with the specified configuration

### Board Game
- **Grid Display**: Visual representation of the board with cells
- **Current Position**: Red circle (●) shows where the player currently is
- **Start Position**: Green 'S' marks the initial starting position
- **Visited Cells**: Light green cells with checkmarks (✓) show visited positions
- **Available Moves**: Yellow highlighted cells show where you can move next
- **Movement Rules**: Can only move to adjacent cells (including diagonals)
- **Reset Functionality**: Button to reset the game to initial state
- **Progress Tracking**: Shows current position and number of visited cells

## How to Play

1. **Setup**: On the start page, configure your board:
   - Choose board dimensions (columns and rows)
   - Set starting coordinates
   - Click "Start Game"

2. **Movement**: 
   - Click on yellow highlighted cells to move
   - You can only move to adjacent cells (8 directions)
   - Try to visit all cells on the board

3. **Visual Indicators**:
   - 🔴 Red circle: Your current position
   - 🟢 Green 'S': Starting position
   - ✅ Light green: Cells you've visited
   - 🟡 Yellow: Available moves

4. **Reset**: Use the "Reset Game" button to start over or "Back to Start" to change board configuration

## Game Objective

The goal is to visit all cells on the board by moving strategically. This is similar to a "Knight's Tour" problem but allows movement in all 8 directions to adjacent cells.

## Technical Details

- Built with React and TypeScript
- Responsive grid layout using CSS Grid
- State management for position tracking and visited cells
- Input validation and error handling
- Comprehensive test coverage

## Components

- `StartPage.tsx`: Configuration interface for setting up the game
- `Board.tsx`: Main game board component with movement logic
- Test files included for both components
