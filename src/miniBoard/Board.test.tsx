import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import Board from "./Board.tsx";

describe("Board Component", () => {
  it("renders board with correct dimensions", () => {
    render(<Board columns={3} rows={3} startX={0} startY={0} />);

    // Check if the board title is rendered
    expect(screen.getByText("Mini Board Game")).toBeInTheDocument();

    // Check if current position is displayed
    expect(screen.getByText("Current Position: (0, 0)")).toBeInTheDocument();

    // Check if visited cells count is displayed
    expect(screen.getByText("Visited Cells: 1")).toBeInTheDocument();
  });

  it("shows start position correctly", () => {
    render(<Board columns={3} rows={3} startX={1} startY={1} />);

    // Check if current position is displayed correctly
    expect(screen.getByText("Current Position: (1, 1)")).toBeInTheDocument();
  });

  it("has reset button", () => {
    render(<Board columns={3} rows={3} startX={0} startY={0} />);

    const resetButton = screen.getByText("Reset Game");
    expect(resetButton).toBeInTheDocument();
  });

  it("displays game instructions", () => {
    render(<Board columns={3} rows={3} startX={0} startY={0} />);

    expect(screen.getByText("How to play:")).toBeInTheDocument();
    expect(
      screen.getByText(/Red circle: Current position/)
    ).toBeInTheDocument();
    expect(screen.getByText(/Green: Start position/)).toBeInTheDocument();
  });
});
