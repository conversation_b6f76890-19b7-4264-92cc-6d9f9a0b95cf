import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import StartPage from './startPage.tsx';

describe('StartPage Component', () => {
  it('renders start page with all input fields', () => {
    render(<StartPage />);
    
    // Check if the title is rendered
    expect(screen.getByText('Mini Board Game')).toBeInTheDocument();
    
    // Check if all input labels are present
    expect(screen.getByText('Number of Columns:')).toBeInTheDocument();
    expect(screen.getByText('Number of Rows:')).toBeInTheDocument();
    expect(screen.getByText('Start X Coordinate:')).toBeInTheDocument();
    expect(screen.getByText('Start Y Coordinate:')).toBeInTheDocument();
    
    // Check if start button is present
    expect(screen.getByText('Start Game')).toBeInTheDocument();
  });

  it('has default values in input fields', () => {
    render(<StartPage />);
    
    // Check default values
    const columnsInput = screen.getByDisplayValue('8');
    const rowsInput = screen.getByDisplayValue('8');
    const startXInput = screen.getByDisplayValue('0');
    const startYInput = screen.getByDisplayValue('0');
    
    expect(columnsInput).toBeInTheDocument();
    expect(rowsInput).toBeInTheDocument();
    expect(startXInput).toBeInTheDocument();
    expect(startYInput).toBeInTheDocument();
  });

  it('allows changing input values', () => {
    render(<StartPage />);
    
    const columnsInput = screen.getByDisplayValue('8');
    fireEvent.change(columnsInput, { target: { value: '5' } });
    
    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
  });

  it('displays instructions', () => {
    render(<StartPage />);
    
    expect(screen.getByText('Instructions:')).toBeInTheDocument();
    expect(screen.getByText(/Set the board dimensions/)).toBeInTheDocument();
    expect(screen.getByText(/Choose starting coordinates/)).toBeInTheDocument();
  });
});
