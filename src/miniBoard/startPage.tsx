import { useState } from "react";
import Board from "./Board.tsx";

interface GameConfig {
  columns: number;
  rows: number;
  startX: number;
  startY: number;
}

export default function StartPage() {
  const [gameConfig, setGameConfig] = useState<GameConfig | null>(null);
  const [columns, setColumns] = useState<string>("8");
  const [rows, setRows] = useState<string>("8");
  const [startX, setStartX] = useState<string>("0");
  const [startY, setStartY] = useState<string>("0");

  const handleStart = () => {
    const config: GameConfig = {
      columns: parseInt(columns),
      rows: parseInt(rows),
      startX: parseInt(startX),
      startY: parseInt(startY)
    };

    // Validate inputs
    if (config.columns <= 0 || config.rows <= 0) {
      alert("Columns and rows must be positive numbers");
      return;
    }

    if (
      config.startX < 0 ||
      config.startX >= config.columns ||
      config.startY < 0 ||
      config.startY >= config.rows
    ) {
      alert("Start coordinates must be within the board boundaries");
      return;
    }

    setGameConfig(config);
  };

  const handleReset = () => {
    setGameConfig(null);
  };

  if (gameConfig) {
    return (
      <div>
        <button
          onClick={handleReset}
          style={{
            marginBottom: "20px",
            padding: "10px 20px",
            backgroundColor: "#f44336",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          Back to Start
        </button>
        <Board
          columns={gameConfig.columns}
          rows={gameConfig.rows}
          startX={gameConfig.startX}
          startY={gameConfig.startY}
        />
      </div>
    );
  }

  return (
    <div
      style={{
        padding: "20px",
        maxWidth: "400px",
        margin: "0 auto",
        fontFamily: "Arial, sans-serif"
      }}
    >
      <h1 style={{ textAlign: "center", marginBottom: "30px" }}>
        Mini Board Game
      </h1>

      <div style={{ marginBottom: "20px" }}>
        <label
          style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}
        >
          Number of Columns:
        </label>
        <input
          type="number"
          value={columns}
          onChange={(e) => setColumns(e.target.value)}
          min="1"
          max="20"
          style={{
            width: "100%",
            padding: "8px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            fontSize: "16px"
          }}
        />
      </div>

      <div style={{ marginBottom: "20px" }}>
        <label
          style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}
        >
          Number of Rows:
        </label>
        <input
          type="number"
          value={rows}
          onChange={(e) => setRows(e.target.value)}
          min="1"
          max="20"
          style={{
            width: "100%",
            padding: "8px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            fontSize: "16px"
          }}
        />
      </div>

      <div style={{ marginBottom: "20px" }}>
        <label
          style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}
        >
          Start X Coordinate:
        </label>
        <input
          type="number"
          value={startX}
          onChange={(e) => setStartX(e.target.value)}
          min="0"
          style={{
            width: "100%",
            padding: "8px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            fontSize: "16px"
          }}
        />
      </div>

      <div style={{ marginBottom: "30px" }}>
        <label
          style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}
        >
          Start Y Coordinate:
        </label>
        <input
          type="number"
          value={startY}
          onChange={(e) => setStartY(e.target.value)}
          min="0"
          style={{
            width: "100%",
            padding: "8px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            fontSize: "16px"
          }}
        />
      </div>

      <button
        onClick={handleStart}
        style={{
          width: "100%",
          padding: "12px",
          backgroundColor: "#4CAF50",
          color: "white",
          border: "none",
          borderRadius: "4px",
          fontSize: "18px",
          cursor: "pointer",
          fontWeight: "bold"
        }}
      >
        Start Game
      </button>

      <div
        style={{
          marginTop: "20px",
          padding: "15px",
          backgroundColor: "#f5f5f5",
          borderRadius: "4px",
          fontSize: "14px",
          color: "#666"
        }}
      >
        <strong>Instructions:</strong>
        <ul style={{ marginTop: "10px", paddingLeft: "20px" }}>
          <li>Set the board dimensions (columns and rows)</li>
          <li>Choose starting coordinates (0-indexed)</li>
          <li>Click "Start Game" to create the board</li>
        </ul>
      </div>
    </div>
  );
}
