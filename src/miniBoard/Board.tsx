import { useState } from "react";

interface BoardProps {
  columns: number;
  rows: number;
  startX: number;
  startY: number;
}

interface Position {
  x: number;
  y: number;
}

// Image URLs from the icons8 sets
const IMAGES = {
  currentPosition: "https://img.icons8.com/color/48/bee.png", // Bee for current position
  startPosition: "https://img.icons8.com/color/48/heart.png", // Heart for start position
  visitedCell: "https://img.icons8.com/color/48/button.png", // Button for visited cells
  emptyCell: "https://img.icons8.com/color/48/cross.png" // Cross for empty cells
};

export default function Board({ columns, rows, startX, startY }: BoardProps) {
  const [currentPosition, setCurrentPosition] = useState<Position>({
    x: startX,
    y: startY
  });
  const [visitedCells, setVisitedCells] = useState<Set<string>>(
    new Set([`${startX},${startY}`])
  );

  const getCellKey = (x: number, y: number) => `${x},${y}`;

  const isCellVisited = (x: number, y: number) => {
    return visitedCells.has(getCellKey(x, y));
  };

  const isCurrentPosition = (x: number, y: number) => {
    return currentPosition.x === x && currentPosition.y === y;
  };

  const isStartPosition = (x: number, y: number) => {
    return startX === x && startY === y;
  };

  const handleCellClick = (x: number, y: number) => {
    // Only allow movement to adjacent cells (including diagonals)
    const dx = Math.abs(currentPosition.x - x);
    const dy = Math.abs(currentPosition.y - y);

    if (dx <= 1 && dy <= 1 && (dx !== 0 || dy !== 0)) {
      setCurrentPosition({ x, y });
      setVisitedCells((prev) => new Set([...prev, getCellKey(x, y)]));
    }
  };

  const resetGame = () => {
    setCurrentPosition({ x: startX, y: startY });
    setVisitedCells(new Set([getCellKey(startX, startY)]));
  };

  const getCellStyle = (x: number, y: number) => {
    const baseStyle = {
      width: "40px",
      height: "40px",
      border: "1px solid #333",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      fontSize: "12px",
      fontWeight: "bold",
      transition: "all 0.2s ease"
    };

    if (isCurrentPosition(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#ff4444",
        color: "white",
        transform: "scale(1.1)",
        zIndex: 10,
        position: "relative" as const
      };
    }

    if (isStartPosition(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#4CAF50",
        color: "white"
      };
    }

    if (isCellVisited(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#e8f5e8",
        color: "#2e7d32"
      };
    }

    // Check if cell is adjacent to current position (can be moved to)
    const dx = Math.abs(currentPosition.x - x);
    const dy = Math.abs(currentPosition.y - y);
    const isAdjacent = dx <= 1 && dy <= 1 && (dx !== 0 || dy !== 0);

    if (isAdjacent) {
      return {
        ...baseStyle,
        backgroundColor: "#fff3cd",
        color: "#856404"
      };
    }

    return {
      ...baseStyle,
      backgroundColor: "#f8f9fa",
      color: "#6c757d"
    };
  };

  const getCellContent = (x: number, y: number) => {
    if (isCurrentPosition(x, y)) {
      return (
        <img
          src={IMAGES.currentPosition}
          alt="Current position"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }
    if (isStartPosition(x, y) && !isCurrentPosition(x, y)) {
      return (
        <img
          src={IMAGES.startPosition}
          alt="Start position"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }
    if (isCellVisited(x, y)) {
      return (
        <img
          src={IMAGES.visitedCell}
          alt="Visited cell"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }
    return (
      <img
        src={IMAGES.emptyCell}
        alt={`Cell ${x},${y}`}
        style={{ width: "32px", height: "32px", opacity: 0.3 }}
      />
    );
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        fontFamily: "Arial, sans-serif"
      }}
    >
      <div style={{ marginBottom: "20px", textAlign: "center" }}>
        <h2>Mini Board Game</h2>
        <p>
          Current Position: ({currentPosition.x}, {currentPosition.y})
        </p>
        <p>Visited Cells: {visitedCells.size}</p>
        <button
          onClick={resetGame}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            marginTop: "10px"
          }}
        >
          Reset Game
        </button>
      </div>

      <table
        style={{
          border: "2px solid #333",
          backgroundColor: "#fff",
          borderRadius: "8px",
          boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          borderCollapse: "separate",
          borderSpacing: "1px",
          padding: "10px"
        }}
      >
        <tbody>
          {Array.from({ length: rows }, (_, y) => (
            <tr key={`row-${y}`}>
              {Array.from({ length: columns }, (_, x) => (
                <td
                  key={getCellKey(x, y)}
                  style={getCellStyle(x, y)}
                  onClick={() => handleCellClick(x, y)}
                  title={`Position: (${x}, ${y})`}
                >
                  {getCellContent(x, y)}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      <div
        style={{
          marginTop: "20px",
          padding: "15px",
          backgroundColor: "#f8f9fa",
          borderRadius: "4px",
          maxWidth: "400px",
          fontSize: "14px"
        }}
      >
        <strong>How to play:</strong>
        <ul style={{ marginTop: "10px", paddingLeft: "20px" }}>
          <li>
            <img
              src={IMAGES.currentPosition}
              alt="bee"
              style={{ width: "16px", height: "16px", verticalAlign: "middle" }}
            />{" "}
            Bee: Current position
          </li>
          <li>
            <img
              src={IMAGES.startPosition}
              alt="heart"
              style={{ width: "16px", height: "16px", verticalAlign: "middle" }}
            />{" "}
            Heart: Start position
          </li>
          <li>
            <img
              src={IMAGES.visitedCell}
              alt="button"
              style={{ width: "16px", height: "16px", verticalAlign: "middle" }}
            />{" "}
            Button: Visited cells
          </li>
          <li>
            <img
              src={IMAGES.emptyCell}
              alt="cross"
              style={{
                width: "16px",
                height: "16px",
                verticalAlign: "middle",
                opacity: 0.3
              }}
            />{" "}
            Cross: Empty cells
          </li>
          <li>Yellow highlighted cells: Available moves (adjacent cells)</li>
          <li>Click on available cells to move</li>
          <li>Try to visit all cells on the board!</li>
        </ul>
      </div>
    </div>
  );
}
