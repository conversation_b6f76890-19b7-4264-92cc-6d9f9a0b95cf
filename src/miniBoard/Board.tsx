import { useState, useCallback } from "react";
import styled from "styled-components";
import beeIcon from "../assets/icons8-bee-50.png";
import heartIcon from "../assets/icons8-heart-50.png";
import crossIcon from "../assets/icons8-cross-50.png";

interface BoardProps {
  columns: number;
  rows: number;
  startX: number;
  startY: number;
}

interface Position {
  x: number;
  y: number;
}

// Local image imports
const IMAGES = {
  currentPosition: beeIcon,
  visitedCell: crossIcon,
  emptyCell: heartIcon
};

export default function Board({ columns, rows, startX, startY }: BoardProps) {
  const [currentPosition, setCurrentPosition] = useState<Position>({
    x: startX,
    y: startY
  });
  const [visitedCells, setVisitedCells] = useState<Set<string>>(
    new Set([`${startX},${startY}`])
  );

  const getCellKey = (x: number, y: number) => `${x},${y}`;

  const isCellVisited = (x: number, y: number) => {
    return visitedCells.has(getCellKey(x, y));
  };

  const isCurrentPosition = (x: number, y: number) => {
    return currentPosition.x === x && currentPosition.y === y;
  };

  const movePlayer = useCallback(
    (moveType: "L1" | "L2" | "L3" | "L4" | "R1" | "R2" | "R3" | "R4") => {
      let newX = currentPosition.x;
      let newY = currentPosition.y;

      switch (moveType) {
        case "L1":
          // Left 1, Up 2
          newX = currentPosition.x - 1;
          newY = currentPosition.y - 2;
          break;
        case "L2":
          // Left 2, Up 1
          newX = currentPosition.x - 2;
          newY = currentPosition.y - 1;
          break;

        case "L3":
          // Left 2, down 1
          newX = currentPosition.x - 2;
          newY = currentPosition.y + 1;
          break;

        case "L4":
          // Left 1, Up 2
          newX = currentPosition.x - 1;
          newY = currentPosition.y + 2;
          break;
        case "R1":
          // Right 1, Up 2
          newX = currentPosition.x + 1;
          newY = currentPosition.y - 2;
          break;
        case "R2":
          // Right 2, Up 1
          newX = currentPosition.x + 2;
          newY = currentPosition.y - 1;
          break;
        case "R3":
          // Right 2, down 1
          newX = currentPosition.x + 2;
          newY = currentPosition.y + 1;
          break;
        case "R4":
          // Right 1, down 2
          newX = currentPosition.x + 1;
          newY = currentPosition.y + 2;
          break;
      }

      // Check if the new position is within bounds
      if (newX >= 0 && newX < columns && newY >= 0 && newY < rows) {
        setCurrentPosition({ x: newX, y: newY });
        setVisitedCells((prev) => new Set([...prev, getCellKey(newX, newY)]));
      }
    },
    [currentPosition, rows, columns]
  );

  const resetGame = () => {
    setCurrentPosition({ x: startX, y: startY });
    setVisitedCells(new Set([getCellKey(startX, startY)]));
  };

  const getCellStyle = (x: number, y: number) => {
    const baseStyle = {
      width: "40px",
      height: "40px",
      border: "1px solid #333",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      fontSize: "12px",
      fontWeight: "bold",
      transition: "all 0.2s ease"
    };

    if (isCurrentPosition(x, y)) {
      return {
        ...baseStyle,
        // backgroundColor: "black",
        color: "white",
        transform: "scale(1.1)",
        zIndex: 10,
        position: "relative" as const
      };
    }

    if (isCellVisited(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#e8f5e8",
        color: "#2e7d32"
      };
    }

    return {
      ...baseStyle,
      backgroundColor: "red",
      color: "#6c757d"
    };
  };

  const getCellContent = (x: number, y: number) => {
    if (isCurrentPosition(x, y)) {
      return (
        <img
          src={IMAGES.currentPosition}
          alt="Current position"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }

    if (isCellVisited(x, y)) {
      return (
        <img
          src={IMAGES.visitedCell}
          alt="Visited cell"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }
    return (
      <img
        src={IMAGES.emptyCell}
        alt={`Cell ${x},${y}`}
        style={{ width: "32px", height: "32px", opacity: 0.3 }}
      />
    );
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        fontFamily: "Arial, sans-serif"
      }}
    >
      <div style={{ marginBottom: "20px", textAlign: "center" }}>
        <h2>Mini Board Game</h2>
        <p>
          Current Position: ({currentPosition.x}, {currentPosition.y})
        </p>
        <p>Scores: {visitedCells.size}</p>
        <button
          onClick={resetGame}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            marginTop: "10px"
          }}
        >
          Reset Game
        </button>
      </div>

      <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
        {/* Left Navigation Panel */}
        <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
          <Button onClick={() => movePlayer("L1")} title="L1">
            L1
          </Button>
          <Button onClick={() => movePlayer("L2")} title="L2: Left 2, Up 1">
            L2
          </Button>
          <Button onClick={() => movePlayer("L3")} title="L3">
            L3
          </Button>
          <Button onClick={() => movePlayer("L4")} title="L4">
            L4
          </Button>
        </div>

        {/* Game Board */}
        <div
          style={{
            backgroundColor: "#fff",
            borderRadius: "8px",
            padding: "10px",
            display: "flex",
            flexDirection: "column",
            border: "2px solid #333",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
          }}
        >
          {Array.from({ length: rows }, (_, y) => (
            <div key={`row-${y}`} style={{ display: "flex" }}>
              {Array.from({ length: columns }, (_, x) => (
                <div
                  key={getCellKey(x, y)}
                  style={getCellStyle(x, y)}
                  title={`Position: (${x}, ${y})`}
                >
                  {getCellContent(x, y)}
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Right Navigation Panel */}
        <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
          <Button onClick={() => movePlayer("R1")} title="R1: Right 1, Up 2">
            R1
          </Button>
          <Button onClick={() => movePlayer("R2")} title="R1: Right 1, Up 2">
            R2
          </Button>
          <Button onClick={() => movePlayer("R3")} title="R1: Right 1, Up 2">
            R3
          </Button>
          <Button onClick={() => movePlayer("R4")} title="R1: Right 1, Up 2">
            R4
          </Button>
        </div>
      </div>
    </div>
  );
}

const Button = styled.button({
  padding: "10px 15px",
  backgroundColor: "#28a745",
  color: "white",
  border: "none",
  borderRadius: "4px",
  cursor: "pointer",
  fontSize: "16px",
  fontWeight: "bold"
});
