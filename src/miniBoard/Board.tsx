import { useState } from "react";

interface BoardProps {
  columns: number;
  rows: number;
  startX: number;
  startY: number;
}

interface Position {
  x: number;
  y: number;
}

export default function Board({ columns, rows, startX, startY }: BoardProps) {
  const [currentPosition, setCurrentPosition] = useState<Position>({
    x: startX,
    y: startY
  });
  const [visitedCells, setVisitedCells] = useState<Set<string>>(
    new Set([`${startX},${startY}`])
  );

  const getCellKey = (x: number, y: number) => `${x},${y}`;

  const isCellVisited = (x: number, y: number) => {
    return visitedCells.has(getCellKey(x, y));
  };

  const isCurrentPosition = (x: number, y: number) => {
    return currentPosition.x === x && currentPosition.y === y;
  };

  const isStartPosition = (x: number, y: number) => {
    return startX === x && startY === y;
  };

  const resetGame = () => {
    setCurrentPosition({ x: startX, y: startY });
    setVisitedCells(new Set([getCellKey(startX, startY)]));
  };

  const getCellStyle = (x: number, y: number) => {
    const baseStyle = {
      width: "40px",
      height: "40px",
      border: "1px solid #333",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      fontSize: "12px",
      fontWeight: "bold",
      transition: "all 0.2s ease"
    };

    if (isCurrentPosition(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#ff4444",
        color: "white",
        transform: "scale(1.1)",
        zIndex: 10,
        position: "relative" as const
      };
    }

    if (isStartPosition(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#4CAF50",
        color: "white"
      };
    }

    if (isCellVisited(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#e8f5e8",
        color: "#2e7d32"
      };
    }

    // Check if cell is adjacent to current position (can be moved to)
    const dx = Math.abs(currentPosition.x - x);
    const dy = Math.abs(currentPosition.y - y);
    const isAdjacent = dx <= 1 && dy <= 1 && (dx !== 0 || dy !== 0);

    if (isAdjacent) {
      return {
        ...baseStyle,
        backgroundColor: "#fff3cd",
        color: "#856404"
      };
    }

    return {
      ...baseStyle,
      backgroundColor: "#f8f9fa",
      color: "#6c757d"
    };
  };

  const getCellContent = (x: number, y: number) => {
    if (isCurrentPosition(x, y)) {
      return "●";
    }
    if (isStartPosition(x, y) && !isCurrentPosition(x, y)) {
      return "S";
    }
    if (isCellVisited(x, y)) {
      return "✓";
    }
    return `${x},${y}`;
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        fontFamily: "Arial, sans-serif"
      }}
    >
      <div style={{ marginBottom: "20px", textAlign: "center" }}>
        <h2>Mini Board Game</h2>
        <p>
          Current Position: ({currentPosition.x}, {currentPosition.y})
        </p>
        <p>Visited Cells: {visitedCells.size}</p>
        <button
          onClick={resetGame}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            marginTop: "10px"
          }}
        >
          Reset Game
        </button>
      </div>

      <div
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(${columns}, 40px)`,
          gap: "1px",
          border: "2px solid #333",
          padding: "10px",
          backgroundColor: "#fff",
          borderRadius: "8px",
          boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
        }}
      >
        {Array.from({ length: rows }, (_, y) =>
          Array.from({ length: columns }, (_, x) => (
            <div
              key={getCellKey(x, y)}
              style={getCellStyle(x, y)}
              title={`Position: (${x}, ${y})`}
            >
              {getCellContent(x, y)}
            </div>
          ))
        )}
      </div>
    </div>
  );
}
