import { useState, useEffect, useCallback } from "react";
import beeIcon from "../assets/icons8-bee-50.png";
import heartIcon from "../assets/icons8-heart-50.png";
import crossIcon from "../assets/icons8-cross-50.png";

interface BoardProps {
  columns: number;
  rows: number;
  startX: number;
  startY: number;
}

interface Position {
  x: number;
  y: number;
}

// Local image imports
const IMAGES = {
  currentPosition: beeIcon,
  visitedCell: crossIcon,
  emptyCell: heartIcon
};

export default function Board({ columns, rows, startX, startY }: BoardProps) {
  const [currentPosition, setCurrentPosition] = useState<Position>({
    x: startX,
    y: startY
  });
  const [visitedCells, setVisitedCells] = useState<Set<string>>(
    new Set([`${startX},${startY}`])
  );

  const getCellKey = (x: number, y: number) => `${x},${y}`;

  const isCellVisited = (x: number, y: number) => {
    return visitedCells.has(getCellKey(x, y));
  };

  const isCurrentPosition = (x: number, y: number) => {
    return currentPosition.x === x && currentPosition.y === y;
  };

  const movePlayer = useCallback(
    (direction: "up" | "down" | "left" | "right") => {
      let newX = currentPosition.x;
      let newY = currentPosition.y;

      switch (direction) {
        case "up":
          newY = Math.max(0, currentPosition.y - 1);
          break;
        case "down":
          newY = Math.min(rows - 1, currentPosition.y + 1);
          break;
        case "left":
          newX = Math.max(0, currentPosition.x - 1);
          break;
        case "right":
          newX = Math.min(columns - 1, currentPosition.x + 1);
          break;
      }

      // Only move if the new position is different from current
      if (newX !== currentPosition.x || newY !== currentPosition.y) {
        setCurrentPosition({ x: newX, y: newY });
        setVisitedCells((prev) => new Set([...prev, getCellKey(newX, newY)]));
      }
    },
    [currentPosition, rows, columns]
  );

  const resetGame = () => {
    setCurrentPosition({ x: startX, y: startY });
    setVisitedCells(new Set([getCellKey(startX, startY)]));
  };

  // Keyboard navigation support
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case "ArrowUp":
        case "w":
        case "W":
          event.preventDefault();
          movePlayer("up");
          break;
        case "ArrowDown":
        case "s":
        case "S":
          event.preventDefault();
          movePlayer("down");
          break;
        case "ArrowLeft":
        case "a":
        case "A":
          event.preventDefault();
          movePlayer("left");
          break;
        case "ArrowRight":
        case "d":
        case "D":
          event.preventDefault();
          movePlayer("right");
          break;
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => {
      window.removeEventListener("keydown", handleKeyPress);
    };
  }, [movePlayer]); // Include movePlayer as dependency

  const getCellStyle = (x: number, y: number) => {
    const baseStyle = {
      width: "40px",
      height: "40px",
      border: "1px solid #333",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      fontSize: "12px",
      fontWeight: "bold",
      transition: "all 0.2s ease"
    };

    if (isCurrentPosition(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#ff4444",
        color: "white",
        transform: "scale(1.1)",
        zIndex: 10,
        position: "relative" as const
      };
    }

    if (isCellVisited(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#e8f5e8",
        color: "#2e7d32"
      };
    }

    return {
      ...baseStyle,
      backgroundColor: "#f8f9fa",
      color: "#6c757d"
    };
  };

  const getCellContent = (x: number, y: number) => {
    if (isCurrentPosition(x, y)) {
      return (
        <img
          src={IMAGES.currentPosition}
          alt="Current position"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }

    if (isCellVisited(x, y)) {
      return (
        <img
          src={IMAGES.visitedCell}
          alt="Visited cell"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }
    return (
      <img
        src={IMAGES.emptyCell}
        alt={`Cell ${x},${y}`}
        style={{ width: "32px", height: "32px", opacity: 0.3 }}
      />
    );
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        fontFamily: "Arial, sans-serif"
      }}
    >
      <div style={{ marginBottom: "20px", textAlign: "center" }}>
        <h2>Mini Board Game</h2>
        <p>
          Current Position: ({currentPosition.x}, {currentPosition.y})
        </p>
        <p>Visited Cells: {visitedCells.size}</p>
        <button
          onClick={resetGame}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            marginTop: "10px"
          }}
        >
          Reset Game
        </button>
      </div>

      <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
        {/* Left Navigation Panel */}
        <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
          <button
            onClick={() => movePlayer("up")}
            style={{
              padding: "10px 15px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold"
            }}
            title="Move Up"
          >
            ↑
          </button>
          <button
            onClick={() => movePlayer("left")}
            style={{
              padding: "10px 15px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold"
            }}
            title="Move Left"
          >
            ←
          </button>
        </div>

        {/* Game Board */}
        <div
          style={{
            backgroundColor: "#fff",
            borderRadius: "8px",
            padding: "10px",
            display: "flex",
            flexDirection: "column",
            border: "2px solid #333",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
          }}
        >
          {Array.from({ length: rows }, (_, y) => (
            <div key={`row-${y}`} style={{ display: "flex" }}>
              {Array.from({ length: columns }, (_, x) => (
                <div
                  key={getCellKey(x, y)}
                  style={getCellStyle(x, y)}
                  title={`Position: (${x}, ${y})`}
                >
                  {getCellContent(x, y)}
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Right Navigation Panel */}
        <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
          <button
            onClick={() => movePlayer("down")}
            style={{
              padding: "10px 15px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold"
            }}
            title="Move Down"
          >
            ↓
          </button>
          <button
            onClick={() => movePlayer("right")}
            style={{
              padding: "10px 15px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold"
            }}
            title="Move Right"
          >
            →
          </button>
        </div>
      </div>

      {/* Navigation Instructions */}
      <div
        style={{
          marginTop: "20px",
          padding: "15px",
          backgroundColor: "#f8f9fa",
          borderRadius: "4px",
          maxWidth: "500px",
          fontSize: "14px",
          textAlign: "center"
        }}
      >
        <strong>Navigation Controls:</strong>
        <div
          style={{
            marginTop: "10px",
            display: "flex",
            justifyContent: "center",
            gap: "20px"
          }}
        >
          <div>
            <strong>Left Panel:</strong>
            <div>↑ Move Up</div>
            <div>← Move Left</div>
          </div>
          <div>
            <strong>Right Panel:</strong>
            <div>↓ Move Down</div>
            <div>→ Move Right</div>
          </div>
        </div>
        <div style={{ marginTop: "10px", fontSize: "12px", color: "#666" }}>
          Use the arrow buttons or keyboard (Arrow keys / WASD) to navigate
          around the board. The bee will move one cell at a time and mark
          visited cells.
        </div>
      </div>
    </div>
  );
}
