import { useState } from "react";
import beeIcon from "../assets/icons8-bee-50.png";
import heartIcon from "../assets/icons8-heart-50.png";
import crossIcon from "../assets/icons8-cross-50.png";

interface BoardProps {
  columns: number;
  rows: number;
  startX: number;
  startY: number;
}

interface Position {
  x: number;
  y: number;
}

// Local image imports
const IMAGES = {
  currentPosition: beeIcon,
  visitedCell: crossIcon,
  emptyCell: heartIcon
};

export default function Board({ columns, rows, startX, startY }: BoardProps) {
  const [currentPosition, setCurrentPosition] = useState<Position>({
    x: startX,
    y: startY
  });
  const [visitedCells, setVisitedCells] = useState<Set<string>>(
    new Set([`${startX},${startY}`])
  );

  const getCellKey = (x: number, y: number) => `${x},${y}`;

  const isCellVisited = (x: number, y: number) => {
    return visitedCells.has(getCellKey(x, y));
  };

  const isCurrentPosition = (x: number, y: number) => {
    return currentPosition.x === x && currentPosition.y === y;
  };

  const resetGame = () => {
    setCurrentPosition({ x: startX, y: startY });
    setVisitedCells(new Set([getCellKey(startX, startY)]));
  };

  const getCellStyle = (x: number, y: number) => {
    const baseStyle = {
      width: "40px",
      height: "40px",
      border: "1px solid #333",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      fontSize: "12px",
      fontWeight: "bold",
      transition: "all 0.2s ease"
    };

    if (isCurrentPosition(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#ff4444",
        color: "white",
        transform: "scale(1.1)",
        zIndex: 10,
        position: "relative" as const
      };
    }

    if (isCellVisited(x, y)) {
      return {
        ...baseStyle,
        backgroundColor: "#e8f5e8",
        color: "#2e7d32"
      };
    }

    return {
      ...baseStyle,
      backgroundColor: "#f8f9fa",
      color: "#6c757d"
    };
  };

  const getCellContent = (x: number, y: number) => {
    if (isCurrentPosition(x, y)) {
      return (
        <img
          src={IMAGES.currentPosition}
          alt="Current position"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }

    if (isCellVisited(x, y)) {
      return (
        <img
          src={IMAGES.visitedCell}
          alt="Visited cell"
          style={{ width: "32px", height: "32px" }}
        />
      );
    }
    return (
      <img
        src={IMAGES.emptyCell}
        alt={`Cell ${x},${y}`}
        style={{ width: "32px", height: "32px", opacity: 0.3 }}
      />
    );
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        fontFamily: "Arial, sans-serif"
      }}
    >
      <div style={{ marginBottom: "20px", textAlign: "center" }}>
        <h2>Mini Board Game</h2>
        <p>
          Current Position: ({currentPosition.x}, {currentPosition.y})
        </p>
        <p>Visited Cells: {visitedCells.size}</p>
        <button
          onClick={resetGame}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            marginTop: "10px"
          }}
        >
          Reset Game
        </button>
      </div>

      <div
        style={{
          backgroundColor: "#fff",
          borderRadius: "8px",
          padding: "10px"
        }}
      >
        <tbody>
          {Array.from({ length: rows }, (_, y) => (
            <tr key={`row-${y}`}>
              {Array.from({ length: columns }, (_, x) => (
                <td
                  key={getCellKey(x, y)}
                  style={getCellStyle(x, y)}
                  title={`Position: (${x}, ${y})`}
                >
                  {getCellContent(x, y)}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
